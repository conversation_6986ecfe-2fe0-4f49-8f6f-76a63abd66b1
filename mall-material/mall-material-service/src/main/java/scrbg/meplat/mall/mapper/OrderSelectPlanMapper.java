package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.OrderSelectPlan;

import java.util.List;

/**
 * @描述： Mapper 接口
 * @作者: ye
 * @日期: 2023-02-28
 */
@Mapper
@Repository
public interface OrderSelectPlanMapper extends BaseMapper<OrderSelectPlan> {

    /**
     * 查询已审核计划的供应商列表
     * @param page 分页参数
     * @param orgId 机构ID
     * @param productType 产品类型
     * @param keywords 关键词
     * @return 供应商列表
     */
    List<OrderSelectPlan> getContactSupplierWithAuditedPlan(Page<OrderSelectPlan> page,
                                                           @Param("orgId") String orgId,
                                                           @Param("productType") Integer productType,
                                                           @Param("keywords") String keywords);

}