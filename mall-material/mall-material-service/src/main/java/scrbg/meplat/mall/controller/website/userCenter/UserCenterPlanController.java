package scrbg.meplat.mall.controller.website.userCenter;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.common.constant.PlanConstants;
import scrbg.meplat.mall.config.permission.RequestPermission;
import scrbg.meplat.mall.dto.plan.PlanDTO;
import scrbg.meplat.mall.dto.plan.PlanExcelDto;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanAudit;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.impl.plan.PlanAuditService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

/**
 * @描述：采购计划主表控制类
 * @作者: ye
 * @日期: 2025-05-27
 */
@Slf4j
@RestController
@RequestMapping("/userCenter/plan")
@Api(tags = "采购计划主表")
public class UserCenterPlanController {

    @Autowired
    private PlanService planService;
    @Autowired
    private PlanAuditService planAuditService;

    @GetMapping("/page")
    @ApiOperation(value = "根据实体属性分页查询")
    public PageR<Plan> listByPage(PlanDTO dto) {
        PageUtils<Plan> page = planService.queryPage(dto);
        return PageR.success(page);
    }

    @GetMapping("/findId")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R<Plan> findById(String id) {
        Plan plan = planService.getById(id);
        return R.success(plan);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody @Valid Plan plan) {
        planService.create(plan);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody @Valid Plan plan) {
        planService.update(plan);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "path")
    })
    public R delete(String id) {
        planService.delete(id);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        planService.deleteBatch(ids);
        return R.success();
    }
    @PostMapping("/delete_plan_and_details/{id}")
    @ApiOperation(value = "删除计划及其明细")
    public R<Void> deletePlanAndDetails(@PathVariable String id) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (!isAuditor(user)) {
            throw new BusinessException("无权限");
        }
        Plan plan = planService.getByBillId(id);
        if (plan == null) {
            throw new BusinessException("计划不存在");
        }
        // 审核人审核不通过或经办人撤回的可以被删除
        if (!plan.getState().equals(PlanConstants.STATE_CANCELED) && !plan.getState().equals(PlanConstants.STATE_REVIEW_REJECTED)) {
            throw new BusinessException("计划状态异常");
        }
        planService.deletePlanAndDetails(id);
        return R.success();
    }

    /**
     * 下载采购计划Excel文件。
     *
     */
    @GetMapping("excel")
    @ApiOperation(value = "下载采购计划Excel文件")
    public void downloadPlanExcel(PlanDTO dto, HttpServletResponse response) {
        try {
            // 1. 设置响应头，告诉浏览器这是一个文件下载
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止文件名中文乱码
            String fileName = URLEncoder.encode("采购计划列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 2. 从服务层获取并转换数据
            dto.setPage(1);
            dto.setLimit(1000000);
            PageUtils<Plan> page = planService.queryPage(dto);
            
            
            List<PlanExcelDto> data = page.getList().stream().map(planService::mapToPlanExcel).collect(Collectors.toList());

            log.info("开始导出采购计划Excel，数据量：{}条", data.size());

            // 3. 使用EasyExcel写入数据到响应输出流
            EasyExcel.write(response.getOutputStream(), PlanExcelDto.class)
                     .sheet("采购计划") // 设置sheet名称
                     .doWrite(data);    // 写入数据

            log.info("采购计划Excel导出成功！");

        } catch (IOException e) {
            log.error("导出采购计划Excel失败：{}", e.getMessage(), e);
            throw new BusinessException(500, "生成excel失败");
        }
    }

    @PostMapping("{id}/state/{state}")
    @ApiOperation(value = "新增")
    @Transactional
//    @RequestPermission(value={"wzcgptscqt:grzx:jhlb:lxcgjh:audit"})
    public R<Void> changeState(@PathVariable String id, @PathVariable String state, @RequestBody PlanAudit planAudit) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        Plan plan = planService.getByBillId(id);
        if (plan == null) {
            throw new BusinessException("计划不存在");
        }
        //待提交的可以被经办人再次提交或 审核人删除
        if (plan.getState().equals(PlanConstants.STATE_PENDING_SUBMISSION)) {
            if (!state.equals(PlanConstants.STATE_PENDING_REVIEW)) {
                throw new BusinessException("计划状态异常");
            }
            if (!isHandler(user)) {
                throw new BusinessException("无权限");
            }
        }
        //待审核的可以被审核人审核成功或者失败 也可以被经办人撤回
        if (plan.getState().equals(PlanConstants.STATE_PENDING_REVIEW)) {
            if (state.equals(PlanConstants.STATE_REVIEWED) || state.equals(PlanConstants.STATE_REVIEW_REJECTED)) {
                if (!isAuditor(user)) {
                    throw new BusinessException("无权限");
                }
            }else if (state.equals(PlanConstants.STATE_PENDING_SUBMISSION)) {
                if (!isHandler(user)) {
                    throw new BusinessException("无权限");
                }
            } else {
                throw new BusinessException("无权限");
            }
        }
        //审核失败的可以被审核人作废
        if (plan.getState().equals(PlanConstants.STATE_REVIEW_REJECTED)) {
            if (!state.equals(PlanConstants.STATE_CANCELED)) {
                throw new BusinessException("计划状态异常");
            }
            if (!isAuditor(user)) {
                throw new BusinessException("无权限");
            }
        }
        if (!plan.getOrgId().equals(user.getOrgId())) {
            throw new BusinessException("只能处理本项目部的计划");
        }
        plan.setState(state);
        planService.updateById(plan);
        // 如果是审核不通过，需要声明理由
        if (state.equals(PlanConstants.STATE_REVIEW_REJECTED)) {
            planAudit.setBillId(id);
            planAudit.setId(IdWorker.getIdStr());
            planAuditService.save(planAudit);
        }
        if (state.equals(PlanConstants.STATE_REVIEWED)) {
            // TODO 审核通过 需要去tt把代办消息状态改为已办，可能要修改订单状态？
        }

        return R.success();
    }

    private boolean isHandler( UserLogin user) {
        // TODO 还未实现的功能 暂时这样方便测试
        // return user.getRoles().contains("计划-经办人");
        return true;
    }

    private boolean isAuditor( UserLogin user) {
        // TODO 还未实现的功能 暂时这样方便测试
        // return user.getRoles().contains("计划-审核人");
        return true;
    }

    @GetMapping("{planId}/audit_infos")
    @ApiOperation(value = "获取审核记录")
    public R<List<PlanAudit>> planAudit(@PathVariable String planId) {
        List<PlanAudit> planAudits = planAuditService.lambdaQuery().eq(PlanAudit::getBillId, planId).orderByDesc(PlanAudit::getGmtCreate).list();
        // 只查询最近的10条
        if (planAudits.size()>10) {
            planAudits = planAudits.subList(0, 10);
        }
        return R.success(planAudits);
    }
}

