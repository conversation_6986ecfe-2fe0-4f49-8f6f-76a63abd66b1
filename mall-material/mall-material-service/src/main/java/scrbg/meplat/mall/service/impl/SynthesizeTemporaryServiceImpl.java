package scrbg.meplat.mall.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;

import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.bidding.AuditBusinessDTO;
import scrbg.meplat.mall.dto.bidding.SynthesizeTemporaryDto;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanDtlEX;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanSave;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.AuditRecord;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.BiddingBidRecordItem;
import scrbg.meplat.mall.entity.BiddingInvitationRelevance;
import scrbg.meplat.mall.entity.BiddingProduct;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.StAttachment;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingBidRecordItemMapper;
import scrbg.meplat.mall.mapper.SynthesizeTemporaryMapper;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan.MaterialAmount;
import scrbg.meplat.mall.service.AuditRecordService;
import scrbg.meplat.mall.service.BiddingBidRecordItemService;
import scrbg.meplat.mall.service.BiddingBidRecordService;
import scrbg.meplat.mall.service.BiddingInvitationRelevanceService;
import scrbg.meplat.mall.service.BiddingProductService;
import scrbg.meplat.mall.service.BiddingPurchaseService;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.service.pcwpApi.Pcwp1Servise;
import scrbg.meplat.mall.service.st.StAttachmentService;
import scrbg.meplat.mall.util.CodeGenerator;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.PCWP2ApiUtil;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailItemVO;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO;
import scrbg.meplat.mall.vo.supplier.SynthesizeTemporaryExportExcelItemVO;

/**
 * @描述：大宗临购单 服务类
 * @作者: ye
 * @日期: 2023-10-07
 */
@Service
public class SynthesizeTemporaryServiceImpl extends ServiceImpl<SynthesizeTemporaryMapper, SynthesizeTemporary> implements SynthesizeTemporaryService {

    @Autowired
    MallConfig mallConfig;
    @Autowired
    SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @Autowired
    AuditRecordService auditRecordService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    OrderSelectPlanService orderSelectPlanService;

    @Autowired
    SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;
    @Autowired
    SystemParamService systemParamService;

    @Autowired
    ProductService productService;

    @Autowired
    ProductSkuService productSkuService;


    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Resource
    private  ShopService shopService;

    @Resource
    private  ShoppingCartService shoppingCartService;
    @Resource
    private BiddingPurchaseService biddingPurchaseService;
    @Resource
    private BiddingProductService biddingProductService;
    @Resource
    private BiddingInvitationRelevanceService biddingInvitationRelevanceService;
    @Autowired
    private PcwpService pcwpService;
    @Autowired
    private StAttachmentService stAttachmentService;
    @Autowired
    private BiddingBidRecordItemMapper biddingBidRecordItemMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q) {
        String keywords = (String) jsonObject.get("keywords");

        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(SynthesizeTemporary::getSynthesizeTemporarySn, keywords)
                        .or()
                        .like(SynthesizeTemporary::getOrgName, keywords)
                        .or()
                        .like(SynthesizeTemporary::getSupplierOrgName, keywords);
            });
        }

        IPage<SynthesizeTemporary> page = this.page(
                new Query<SynthesizeTemporary>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SynthesizeTemporary synthesizeTemporary) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(synthesizeTemporary);
    }

    @Override
    public void update(SynthesizeTemporary synthesizeTemporary) {
        super.updateById(synthesizeTemporary);
    }


    @Override
    public SynthesizeTemporary getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 新增大宗清单
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSynthesizeTemporary(SynthesizeTemporary vo) {
        // TODO 去总计划校验总计划是否有同名称、同规格、且有剩余数量的物资
        BigDecimal referenceSumAmount = new BigDecimal(0);
        BigDecimal synthesizeSumAmount = new BigDecimal(0);
        // 计算金额
        for (SynthesizeTemporaryDtl p : vo.getDtls()) {
            // 只是校验
            Integer isTwoUnit = p.getIsTwoUnit();
            if(isTwoUnit != null && isTwoUnit == 1) {
//                BigDecimal b = p.getTwoUnitNum().divide(p.getSecondUnitNum(), new MathContext(18)).setScale(4, BigDecimal.ROUND_HALF_UP);
//                if(b.compareTo(p.getQty()) != 0) {
//                    throw new BusinessException("商品为：【" + p.getProductName() + "】计算数量前后不一致！");
//                }
            }
            BigDecimal m = p.getQty().multiply(p.getReferencePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            p.setReferenceAmount(m);
            referenceSumAmount = referenceSumAmount.add(m);

            BigDecimal m2 = p.getQty().multiply(p.getSynthesizePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            p.setSynthesizeAmount(m2);
            synthesizeSumAmount = synthesizeSumAmount.add(m2);
        }

        SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, PublicEnum.SYNTHESIZE_TEMPORARY_AMOUNT_MAX.getRemark()).one();
        String keyValue = systemParam.getKeyValue();
        // 比较总金额不能超过
        BigDecimal maxAmount = new BigDecimal(keyValue);
        // 单位是万，需要乘以
        if (maxAmount.multiply(new BigDecimal("10000")).compareTo(referenceSumAmount) == -1) {
            throw new BusinessException("选择商品总金额超过平台限制金额！限制最大金额为：" + maxAmount.toPlainString() + "万");
        }

        if(synthesizeSumAmount.compareTo(vo.getSynthesizeSumAmount()) != 0) {
            throw new BusinessException("金额计算前后不一致！");
        }
        vo.setReferenceSumAmount(referenceSumAmount);
        vo.setSynthesizeSumAmount(synthesizeSumAmount);

        // 获取当前机构是否有草稿状态的数据
        // 如果有追加数据，没有新增数据
        SynthesizeTemporary one = lambdaQuery()
                .eq(SynthesizeTemporary::getOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId())
                .eq(SynthesizeTemporary::getSupplierOrgId, vo.getSupplierOrgId())
                .eq(SynthesizeTemporary::getState, 0)
                .one();
        List<SynthesizeTemporaryDtl> dtls;
        if (one == null) {
            // 新增
//            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
//            vo.setOrgId(currentUser.getOrgId());
//            vo.setOrgName(currentUser.getEnterpriseName());
            vo.setSynthesizeTemporarySn(IdWorker.getIdStr());
            vo.setSynthesizeTemporaryId(null);
            Integer isSubmit = vo.getIsSubmit();
            if (isSubmit != null && isSubmit == 1) {
                vo.setState(1);
                vo.setSubmitTime(new Date());
            }
            save(vo);
            for (SynthesizeTemporaryDtl dtl : vo.getDtls()) {
                dtl.setSynthesizeTemporaryId(vo.getSynthesizeTemporaryId());
                dtl.setSynthesizeTemporaryDtlId(null);
                synthesizeTemporaryDtlService.save(dtl);
            }
            dtls = vo.getDtls();
            List<StAttachment> attachments = vo.getAttachments();
            if (!attachments.isEmpty()) {
                String stId = vo.getSynthesizeTemporaryId();
                attachments.forEach(a->a.setStId(stId));
                stAttachmentService.saveBatch(attachments);
            }
        } else {
            // 不等于null累加数据
            if (!StringUtils.isBlank(vo.getRemarks())) {
                one.setRemarks(vo.getRemarks());
            }
            one.setProvince(vo.getProvince());
            one.setCity(vo.getCity());
            one.setCounty(vo.getCounty());
            one.setBillType(vo.getBillType());
            one.setReceiverAddress(vo.getReceiverAddress());
            one.setPaymentWeek(vo.getPaymentWeek());
            one.setOutPhaseInterest(vo.getOutPhaseInterest());
            for (SynthesizeTemporaryDtl dtl : vo.getDtls()) {
                dtl.setSynthesizeTemporaryDtlId(null);
                dtl.setSynthesizeTemporaryId(one.getSynthesizeTemporaryId());
                SynthesizeTemporaryDtl dataDtl = synthesizeTemporaryDtlService.lambdaQuery()
                        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, one.getSynthesizeTemporaryId())
                        .eq(SynthesizeTemporaryDtl::getProductId, dtl.getProductId()).one();
                if (dataDtl != null) {
                    dtl.setSynthesizeTemporaryDtlId(dataDtl.getSynthesizeTemporaryDtlId());
                    // 如果存在只是数量累加
                    if(dtl.getIsTwoUnit() != null && dtl.getIsTwoUnit() == 1) {
                        // 如果源数据也是有二级单位
                        if(dataDtl.getIsTwoUnit() != null && dataDtl.getIsTwoUnit() == 1) {
                            dtl.setQty(dataDtl.getQty().add(dtl.getQty()));
                            BigDecimal twoUnitNum = dtl.getQty().multiply(dtl.getSecondUnitNum()).setScale(4, BigDecimal.ROUND_HALF_UP);
                            dtl.setTwoUnitNum(twoUnitNum);
                            dtl.setReferenceAmount(dtl.getQty().multiply(dtl.getReferencePrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                            dtl.setSynthesizeAmount(dtl.getQty().multiply(dtl.getSynthesizePrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                        }else {
                            // 没有二级单位，直接使用新的，之前的数量覆盖掉，如果想要累加数量会出现反推，所以直接覆盖
                        }
                    }else {
                        dtl.setQty(dataDtl.getQty().add(dtl.getQty()));
                        dtl.setReferenceAmount(dtl.getQty().multiply(dtl.getReferencePrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                        dtl.setSynthesizeAmount(dtl.getQty().multiply(dtl.getSynthesizePrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    synthesizeTemporaryDtlService.update(dtl);
                } else {
                    synthesizeTemporaryDtlService.save(dtl);
                }
            }
            // 重新计算金额
            dtls = synthesizeTemporaryDtlService.lambdaQuery()
                    .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, one.getSynthesizeTemporaryId())
                    .select(SynthesizeTemporaryDtl::getSynthesizeAmount, SynthesizeTemporaryDtl::getReferenceAmount)
                    .list();
            BigDecimal referenceSumAmount1 = new BigDecimal(0);
            BigDecimal synthesizeSumAmount1 = new BigDecimal(0);
//            HashMap<String, Integer> map = new HashMap<>();

            for (SynthesizeTemporaryDtl dtl : dtls) {
                referenceSumAmount1 = referenceSumAmount1.add(dtl.getReferenceAmount());
                synthesizeSumAmount1 = synthesizeSumAmount1.add(dtl.getSynthesizeAmount());

//                // 校验，不能出现相同的清单数据 需求说：pcwp限制
//                String key = dtl.getMaterialName() + "---" + dtl.getClassIdPath() + "---" +  dtl.getTexture();
//                boolean b = map.containsKey(key);
//                if(b) {
//                    map.put(key,map.get(key) + 1);
//                }else {
//                    map.put(key,0);
//                }
//            }
//
//            for (String s : map.keySet()) {
//                if(map.get(s) > 0) {
//                    throw new BusinessException("当前清单 或 历史草稿清单明细存在相同商品：" + s);
//                }
            }
            one.setReferenceSumAmount(referenceSumAmount1);
            one.setSynthesizeSumAmount(synthesizeSumAmount1);

            // 单位是万，需要乘以
            if (maxAmount.multiply(new BigDecimal("10000")).compareTo(referenceSumAmount1) == -1) {
                throw new BusinessException("选择商品+已有草稿清单商品总金额超过平台限制金额！限制最大金额为：" + maxAmount.toPlainString() + "万");
            }
            Integer isSubmit = vo.getIsSubmit();
            if (isSubmit != null && isSubmit == 1) {
                one.setState(1);
                one.setSubmitTime(new Date());
            }
            update(one);
            List<StAttachment> attachments = vo.getAttachments();
            if (!attachments.isEmpty()) {
                String stId = one.getSynthesizeTemporaryId();
                stAttachmentService.lambdaUpdate().eq(StAttachment::getStId, stId).remove();
                attachments.forEach(a->a.setStId(stId));
                stAttachmentService.saveBatch(attachments);
            }
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        // TODO 周转材料是否需要去总价华验证数量是否充足
        verifyPlan(dtls, currentUser.getOrgId());

    }

    private void verifyPlan(List<SynthesizeTemporaryDtl> dtls, String orgId){
        List<MaterialAmount> materials = dtls.stream().map(d->{
            return MaterialAmount.builder().amount(d.getQty().intValue()).materialId(d.getMaterialId()).build();
        }).collect(Collectors.toList());
        PcwpRes<Boolean> result = pcwpService.verifyPlan(VerifyPlan.builder().materials(materials).orgId(orgId).build());
        if (result.getCode()!=200) {
            throw new BusinessException(result.getMessage());
        }
        if (!result.getData()) {
            throw new BusinessException("总计划商品数量不足，请变更总计划");
        }
    }

    /**
     * 查询当前机构的数据
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getThisOrgList(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q) {
        String keywords = (String) jsonObject.get("keywords");
        Integer stType = (Integer) jsonObject.get("stType");
        if (StringUtils.isBlank(ThreadLocalUtil.getCurrentUser().getEnterpriseId())) {
            throw new BusinessException("当前登陆异常，请重新登陆！");
        }
        if (mallConfig.profilesActive.equals("dev")) {
            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
            // 数据权限控制
            String roleName = "物资下单权限";
            List<MallRole> mallRoles = currentUser.getMallRoles();
            if (org.springframework.util.CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资下单权限权限！");
            }
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            Integer dataSelect = jsonObject.getInteger("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            if (dataSelect == 1 && role.getOrgSearch() == 1){
                // 看全部
                List<String> ids = currentUser.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                List<String> ordIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(ids);
                q.in(SynthesizeTemporary::getOrgId, ordIds);
            }
           else if (dataSelect == 2){
                // 只看自己
                q.eq(SynthesizeTemporary::getOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
            }
           else if (dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定cha
                if (!org.springframework.util.CollectionUtils.isEmpty(dataScopes)) {
                    List<String> ordIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(dataScopes);
                    if (org.springframework.util.CollectionUtils.isEmpty(ordIds)){
                        throw new BusinessException("机构查询失败");
                    }
                    q.in(SynthesizeTemporary::getOrgId, ordIds);
                }else {
                    // 查除自己之外
                    List<String> ids = currentUser.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                    List<String> orgIds = ids.stream().filter(t -> !t.equals(ThreadLocalUtil.getCurrentUser().getOrgId())).collect(Collectors.toList());
                    List<String> localIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(orgIds);
                    q.in(SynthesizeTemporary::getOrgId, localIds);
                }
            }else {
                // 查自己的
                q.eq(SynthesizeTemporary::getOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
            }
        }else {
            q.eq(SynthesizeTemporary::getOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        }
        // 只查询未删除的，当状态是3供应商已确认，进行删除时这个属性会修改成1,
        q.eq(SynthesizeTemporary::getOrgIsDelete, 0);
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(SynthesizeTemporary::getSynthesizeTemporarySn, keywords)
                        .or()
                        .like(SynthesizeTemporary::getOrgName, keywords)
                        .or()
                        .like(SynthesizeTemporary::getSupplierOrgName, keywords);
            });
        }
        if (stType != null) {
            q.eq(SynthesizeTemporary::getStType, stType);
        }
        q.orderByDesc(SynthesizeTemporary::getGmtCreate);
        IPage<SynthesizeTemporary> page = this.page(
                new Query<SynthesizeTemporary>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 根据编号获取数据
     *
     * @param sn (orgId远程)
     * @return
     */
    @Override
    public SynthesizeTemporary getBySn(String sn,String orgId) {
        String enterpriseId = null;
        if (mallConfig.profilesActive.equals("dev")) {
            if (org.apache.commons.lang.StringUtils.isBlank(orgId)){
                 enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
            }else {
                List<String> localIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(Arrays.asList(orgId));
                if (org.springframework.util.CollectionUtils.isEmpty(localIds)){
                    throw new BusinessException("机构查询失败");
                }
                enterpriseId = localIds.get(0);
            }
        }else {
            enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        }

        SynthesizeTemporary one = lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, sn)
                .eq(SynthesizeTemporary::getOrgId, enterpriseId)
                .one();
        if (one == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        List<SynthesizeTemporaryDtl> list = synthesizeTemporaryDtlService.lambdaQuery().eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, one.getSynthesizeTemporaryId()).list();
        one.setDtls(list);
        // 获取审核历史
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 9)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, one.getSynthesizeTemporaryId()).list();
        one.setAuditRecords(auditRecords);
        // 获取报价单
        List<GetBidingRecordItemVO> quotations = biddingBidRecordItemMapper.getQuotationsBySynTempSn(sn);
        one.setQuotations(quotations);
        return one;
    }

    /**
     * 修改数据
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInfo(SynthesizeTemporary dto) {
        if (dto.getState() == 0 || dto.getState() == 4 || dto.getState() == 11) {
            BigDecimal referenceSumAmount = new BigDecimal(0);
            BigDecimal synthesizeSumAmount = new BigDecimal(0);
            // 计算金额
            for (SynthesizeTemporaryDtl p : dto.getDtls()) {
                BigDecimal m = p.getQty().multiply(p.getReferencePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                p.setReferenceAmount(m);
                referenceSumAmount = referenceSumAmount.add(m);

                BigDecimal m2 = p.getQty().multiply(p.getSynthesizePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                p.setSynthesizeAmount(m2);
                synthesizeSumAmount = synthesizeSumAmount.add(m2);
            }
            dto.setReferenceSumAmount(referenceSumAmount);
            dto.setSynthesizeSumAmount(synthesizeSumAmount);
            Integer isSubmit = dto.getIsSubmit();
            if (isSubmit != null && isSubmit == 1) {
                dto.setState(1);
                dto.setSubmitTime(new Date());
            }
            update(dto);
            for (SynthesizeTemporaryDtl dtl : dto.getDtls()) {
                synthesizeTemporaryDtlService.update(dtl);
            }

            // 重新计算金额
            List<SynthesizeTemporaryDtl> dtls = synthesizeTemporaryDtlService.lambdaQuery()
                    .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, dto.getSynthesizeTemporaryId())
                    .select(SynthesizeTemporaryDtl::getSynthesizeAmount, SynthesizeTemporaryDtl::getReferenceAmount)
                    .list();
            BigDecimal synthesizeSumAmount1 = new BigDecimal(0);
            for (SynthesizeTemporaryDtl dtl : dtls) {
                synthesizeSumAmount1 = synthesizeSumAmount1.add(dtl.getSynthesizeAmount());
            }
            SystemParam systemParam = systemParamService.lambdaQuery().eq(SystemParam::getCode, PublicEnum.SYNTHESIZE_TEMPORARY_AMOUNT_MAX.getRemark()).one();
            String keyValue = systemParam.getKeyValue();
            // 比较总金额不能超过
            BigDecimal maxAmount = new BigDecimal(keyValue);
            // 单位是万，需要乘以
            if (maxAmount.multiply(new BigDecimal("10000")).compareTo(synthesizeSumAmount1) == -1) {
                throw new BusinessException("选择商品总金额超过平台限制金额！限制最大金额为：" + maxAmount.toPlainString() + "万");
            }
        }
    }

    /**
     * 删除数据
     *
     * @param id
     */
    @Override
    public void deleteInfo(String id) {
        // 如果是草稿直接删除，如果是供应商已确认，只是修改标记状态
        SynthesizeTemporary byId = getById(id);
        if (byId != null) {
            if (byId.getState() == 0 || byId.getState() == 11) {
                delete(id);
                synthesizeTemporaryDtlService.lambdaUpdate()
                        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, id)
                        .remove();
            }
            if (byId.getState() == 3) {
                lambdaUpdate().eq(SynthesizeTemporary::getSynthesizeTemporaryId, id)
                        .set(SynthesizeTemporary::getOrgIsDelete, 1)
                        .set(SynthesizeTemporary::getGmtModified, new Date())
                        .update();
            }
        }
    }

    /**
     * 删除明细数据
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInfoItem(String id) {
        SynthesizeTemporaryDtl byId = synthesizeTemporaryDtlService.getById(id);
        if(byId != null) {
            synthesizeTemporaryDtlService.delete(id);
            // 重新计算金额
            List<SynthesizeTemporaryDtl> dtls = synthesizeTemporaryDtlService.lambdaQuery()
                    .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, byId.getSynthesizeTemporaryId())
                    .select(SynthesizeTemporaryDtl::getSynthesizeAmount, SynthesizeTemporaryDtl::getReferenceAmount)
                    .list();
            BigDecimal referenceSumAmount1 = new BigDecimal(0);
            BigDecimal synthesizeSumAmount1 = new BigDecimal(0);
            for (SynthesizeTemporaryDtl dtl : dtls) {
                referenceSumAmount1 = referenceSumAmount1.add(dtl.getReferenceAmount());
                synthesizeSumAmount1 = synthesizeSumAmount1.add(dtl.getSynthesizeAmount());
            }
            lambdaUpdate().eq(SynthesizeTemporary::getSynthesizeTemporaryId,byId.getSynthesizeTemporaryId())
                            .set(SynthesizeTemporary::getReferenceSumAmount,referenceSumAmount1)
                            .set(SynthesizeTemporary::getSynthesizeSumAmount,synthesizeSumAmount1).update();

        }

    }

    /**
     * 供应商查询大宗临购清单
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils supplierListByEntity(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q) {
        String keywords = (String) jsonObject.get("keywords");
        String synthesizeTemporarySn = (String) jsonObject.get("synthesizeTemporarySn");
        String orgName = (String) jsonObject.get("orgName");
        Integer state = (Integer) jsonObject.get("state");
        Integer billType = (Integer) jsonObject.get("billType");
        ArrayList<Integer> states = (ArrayList<Integer>) jsonObject.get("states");
        String startCreateDate = (String) jsonObject.get("startCreateTime");
        String endCreateDate = (String) jsonObject.get("endCreateTime");
        Integer stType = (Integer) jsonObject.get("stType");
        String startSubmitTime = (String) jsonObject.get("startSubmitTime");
        String endSubmitTime = (String) jsonObject.get("endSubmitTime");
        String startAuditTime = (String) jsonObject.get("startAuditTime");
        String endAuditTime = (String) jsonObject.get("endAuditTime");
        Integer bidStatus = (Integer) jsonObject.get("bidStatus");
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        q.eq(SynthesizeTemporary::getSupplierOrgId,enterpriseId);
        if (stType != null ){
            q.eq(SynthesizeTemporary::getStType,stType);
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(SynthesizeTemporary::getSynthesizeTemporarySn, keywords)
                        .or()
                        .like(SynthesizeTemporary::getOrgName, keywords)
                        .or()
                        .like(SynthesizeTemporary::getSupplierOrgName, keywords);
            });
        }
        q.eq(bidStatus!=null,SynthesizeTemporary::getBidStatus,bidStatus);
        q.eq(state != null,SynthesizeTemporary::getState,state);
        q.eq(billType != null,SynthesizeTemporary::getBillType,billType);
        q.in(CollectionUtils.isNotEmpty(states),SynthesizeTemporary::getState,states);
        q.like(StringUtils.isNotBlank(synthesizeTemporarySn),SynthesizeTemporary::getSynthesizeTemporarySn,synthesizeTemporarySn);
        q.like(StringUtils.isNotBlank(orgName),SynthesizeTemporary::getOrgName,orgName);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startCreateDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endCreateDate), SynthesizeTemporary::getGmtCreate, startCreateDate, endCreateDate);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startAuditTime) && org.apache.commons.lang.StringUtils.isNotEmpty(endAuditTime), SynthesizeTemporary::getAuditTime, startAuditTime, endAuditTime);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startSubmitTime) && org.apache.commons.lang.StringUtils.isNotEmpty(endSubmitTime), SynthesizeTemporary::getSubmitTime, startSubmitTime, endSubmitTime);
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        if (orderBy == null || orderBy == 1) {
            q.orderByDesc(SynthesizeTemporary::getGmtCreate);
        } else if (orderBy == 2) {
            q.orderByDesc(SynthesizeTemporary::getAuditTime);
        } else if (orderBy == 3) {
            q.orderByDesc(SynthesizeTemporary::getSubmitTime);
        }
        IPage<SynthesizeTemporary> page = this.page(
                new Query<SynthesizeTemporary>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Resource
    private BiddingBidRecordService biddingBidRecordService;
    @Resource BiddingBidRecordItemService biddingBidRecordItemService;
    /**
     * 根据编号获取清单数据（供应商）
     * @param sn
     * @return
     */
    @Override
    public SynthesizeTemporary getSupplierBilBySn(String sn) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        SynthesizeTemporary one = lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, sn)
                .eq(SynthesizeTemporary::getSupplierOrgId, enterpriseId)
                .one();
        if (one == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        List<SynthesizeTemporaryDtl> list = synthesizeTemporaryDtlService.lambdaQuery().eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, one.getSynthesizeTemporaryId()).list();
        one.setDtls(list);
        // 处理竞价
        if (CollectionUtils.isNotEmpty(list)){
            for (SynthesizeTemporaryDtl synthesizeTemporaryDtl : list) {
                if (synthesizeTemporaryDtl.getIsBidding()!= null && synthesizeTemporaryDtl.getIsBidding() == 1) {
                    List<BiddingProduct> biddingProducts = biddingProductService.lambdaQuery().
                            eq(BiddingProduct::getSynthesizeTemporaryDtlId, synthesizeTemporaryDtl.getSynthesizeTemporaryDtlId()).
                            orderByDesc(BiddingProduct::getGmtCreate).list();
                    // 取最新的一个
                    // 先从多个竞价中拿中标的
                    if (biddingProducts!=null && biddingProducts.size()>0) {
                        for (BiddingProduct product : biddingProducts) {
                            //BiddingProduct product = biddingProducts.get(0);
                            synthesizeTemporaryDtl.setBiddingSn(product.getBiddingSn());
                            BiddingBidRecord bidRecord = biddingBidRecordService.lambdaQuery()
                                    .eq(BiddingBidRecord::getBiddingSn, product.getBiddingSn())
                                    .eq(BiddingBidRecord::getState, 6).one();
                            if (bidRecord != null) {
                                // 这就是竞价中标数据
                                synthesizeTemporaryDtl.setBiddingSn(product.getBiddingSn());
                                BiddingBidRecordItem biddingBidRecordItem = biddingBidRecordItemService.lambdaQuery()
                                        .eq(BiddingBidRecordItem::getBidRecordId, bidRecord.getBidRecordId())
                                        .eq(BiddingBidRecordItem::getBiddingProductId, product.getBiddingProductId()).one();
                                synthesizeTemporaryDtl.setBiddingProduct(product);
                                synthesizeTemporaryDtl.setBidRecordItem(biddingBidRecordItem);
                                break;
                            }
                        }
                        // 中标之前(竞价中标明细为空)都拿最新一个竞价编号
                        if (synthesizeTemporaryDtl.getBidRecordItem() == null){
                            BiddingProduct product = biddingProducts.get(0);
                            synthesizeTemporaryDtl.setBiddingSn(product.getBiddingSn());

                        }
                    }
                }
                }
        }
        // 获取审核历史
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .in(AuditRecord::getRelevanceType, 7,9)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, one.getSynthesizeTemporaryId()).list();
        one.setAuditRecords(auditRecords);
        return one;
    }

    /**
     * 批量修改明细并确认（供应商）
     *
     * @param dtos
     * @param outPhaseInterest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateItems(List<SynthesizeTemporaryDtl> dtos, BigDecimal outPhaseInterest) {
        String synthesizeTemporaryId = dtos.get(0).getSynthesizeTemporaryId();
        SynthesizeTemporary synthesizeTemporary = getById(synthesizeTemporaryId);
        if(synthesizeTemporary == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        // 重新计算
        for (SynthesizeTemporaryDtl dto : dtos) {
            if(synthesizeTemporary.getBillType() == 1) {
                BigDecimal netPrice = dto.getNetPrice();
                BigDecimal fixationPrice = dto.getFixationPrice();
                if(netPrice == null || netPrice.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BusinessException("商品：【" +dto.getProductName() + "】未填入网价！");
                }
                if(fixationPrice == null) {
                    fixationPrice = new BigDecimal(0);
                }
                dto.setSynthesizePrice(netPrice.add(fixationPrice));
                BigDecimal amount = dto.getQty().multiply(netPrice.add(fixationPrice)).setScale(2, BigDecimal.ROUND_HALF_UP);
                dto.setSynthesizeAmount(amount);
            }
            if(synthesizeTemporary.getBillType() == 2) {
                BigDecimal outFactoryPrice = dto.getOutFactoryPrice();
                BigDecimal transportPrice = dto.getTransportPrice();
                if(outFactoryPrice == null || outFactoryPrice.compareTo(BigDecimal.ZERO) == 0 ) {
                    throw new BusinessException("商品：【" +dto.getProductName() + "】未填入出厂价！");
                }
                if(transportPrice == null) {
                    transportPrice =  new BigDecimal(0);
                }
                dto.setSynthesizePrice(outFactoryPrice.add(transportPrice));
                BigDecimal amount = dto.getQty().multiply(outFactoryPrice.add(transportPrice)).setScale(2, BigDecimal.ROUND_HALF_UP);
                dto.setSynthesizeAmount(amount);
            }
        }
       synthesizeTemporaryDtlService.updateBatchById(dtos);
       // 重新计算金额
        List<SynthesizeTemporaryDtl> dtls = synthesizeTemporaryDtlService.lambdaQuery()
                .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, synthesizeTemporary.getSynthesizeTemporaryId())
                .select(SynthesizeTemporaryDtl::getSynthesizeAmount)
                .list();
        BigDecimal synthesizeSumAmount1 = new BigDecimal(0);
        for (SynthesizeTemporaryDtl dtl : dtls) {
            synthesizeSumAmount1 = synthesizeSumAmount1.add(dtl.getSynthesizeAmount());
        }
        int state = 1;
        Integer isAffirm = dtos.get(0).getIsAffirm();
        if(isAffirm == null || isAffirm == 0) {
            state = 1;
        }else if(isAffirm == 1) {
            // 判断是否有权限
            boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资大宗临购清单提交权限");
            if(bb == false) {
                throw new BusinessException("没有权限请联系管理员！");
            }
            state = 2;
        }
        lambdaUpdate().eq(SynthesizeTemporary::getSynthesizeTemporaryId,synthesizeTemporary.getSynthesizeTemporaryId())
                .set(SynthesizeTemporary::getState,state)
                .set(SynthesizeTemporary::getOutPhaseInterest,outPhaseInterest)
                .set(SynthesizeTemporary::getGmtModified,new Date())
                .set(SynthesizeTemporary::getAuditTime,new Date())
                .set(SynthesizeTemporary::getRefuseRes,null)
                .set(SynthesizeTemporary::getSynthesizeSumAmount,synthesizeSumAmount1).update();
    }

    /**
     * 供应商删除单据
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierDeleteInfo(String id) {
        SynthesizeTemporary byId = getById(id);
        if(byId != null) {
            Integer orgIsDelete = byId.getOrgIsDelete();
            if(orgIsDelete != null && orgIsDelete == 1) {
                delete(id);
                synthesizeTemporaryDtlService.lambdaUpdate()
                        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId,id).remove();
            }
        }
    }



    @Autowired
    Pcwp1Servise pcwp1Servise;
    /**
     * 推送大宗临购计划到pcwp
     *
     * @param id
     * @param idStr
     * @param farArg
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSynthesizeTemporaryPlan(String id, String idStr, StringBuilder farArg){
        SynthesizeTemporary byId = getById(id);
        if(byId == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        BulkRetailPlanSave dto = new BulkRetailPlanSave();
        dto.setKeyId(idStr);
        BulkRetailPlanEX a = new BulkRetailPlanEX();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        a.setFounderId(user.getFarUserId());
        a.setFounderName(user.getUserName());

        a.setOrgId(byId.getOrgFarId());
        a.setOrgName(byId.getOrgName());
        BigDecimal synthesizeSumAmount = byId.getSynthesizeSumAmount();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, byId.getSupplierOrgId()).one();
        if(enterpriseInfo == null) {
            throw new BusinessException("供应商数据不存在！");
        }
        BigDecimal taxRate = enterpriseInfo.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        a.setTaxRate(taxRate);
        a.setTotalAmount(synthesizeSumAmount);
        a.setPlanDate(LocalDate.now().toString());
        a.setRemarks(byId.getRemarks());
        a.setPreparerId(user.getFarUserId());
        a.setPreparer(user.getUserName());
        a.setType(byId.getBillType());
        a.setSourceBillId(byId.getSynthesizeTemporaryId());
        a.setSourceBillNo(byId.getSynthesizeTemporarySn());
        a.setOrgShort(enterpriseInfo.getShortCode());
        a.setCreditCode(enterpriseInfo.getSocialCreditCode());
        // 明细

        List<SynthesizeTemporaryDtl> list = synthesizeTemporaryDtlService.lambdaQuery().eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, id).list();
        ArrayList<BulkRetailPlanDtlEX> vos = new ArrayList<>();

        BigDecimal totalNotTaxAmount = new BigDecimal(0);
        for (SynthesizeTemporaryDtl dtl : list) {
            BulkRetailPlanDtlEX b = new BulkRetailPlanDtlEX();
            b.setMaterialClassId(dtl.getClassIdPath());
            b.setMaterialClassName(dtl.getClassNamePath());
            Product productById = productService.validateProduct(dtl.getProductId(), null);
            b.setMaterialId(productById.getRelevanceId());
            b.setMaterialName(productById.getRelevanceName());
            b.setSpec(dtl.getSpec()==null?"":dtl.getSpec());
            b.setUnit(dtl.getUnit());
            // 推送材质    6.6 大宗临购推送计划材质=商品名称+材质
//            if(mallConfig.changAmountAndTaxPlanAmount==0){
//                String texture = dtl.getProductName();
//                if (dtl.getTexture()!=null){
//                     texture = dtl.getProductName()+"["+dtl.getTexture()+"]";
//                }
//                b.setTexture(dtl.getTexture());
//            }else {
//                b.setTexture(dtl.getTexture());
//            }
            b.setTexture(dtl.getTexture());
            b.setQuantity(dtl.getQty());
            b.setNetworkPrice(dtl.getNetPrice());
            BigDecimal price = null;
            if(byId.getBillType() == 1) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dtl.getNetPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dtl.getFixationPrice(), taxRate);
                price = pr1.add(pr2);
                b.setNetworkPrice(pr1);
                b.setFixedFee(pr2);
            }
            if(byId.getBillType() == 2) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dtl.getOutFactoryPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dtl.getTransportPrice(), taxRate);
                price = pr1.add(pr2);
                b.setFactoryPrice(pr1);
                b.setFreight(pr2);
            }
            b.setPrice(price);
            b.setTaxPrice(dtl.getSynthesizePrice());
            BigDecimal amount = TaxCalculator.noTarRateItemAmount(dtl.getSynthesizeAmount(),price,dtl.getQty(), taxRate);
            totalNotTaxAmount = totalNotTaxAmount.add(amount);
            b.setAmount(amount);
            b.setSupplierId(byId.getSupplierOrgId());
            b.setSupplierName(byId.getSupplierOrgName());
            b.setSourceDtlId(dtl.getSynthesizeTemporaryDtlId());
            b.setTradeName(dtl.getProductName());
            b.setTradeId(dtl.getProductId());
            b.setOrgShort(enterpriseInfo.getShortCode());
            b.setCreditCode(enterpriseInfo.getSocialCreditCode());
            vos.add(b);
        }
        a.setDetails(vos);
        a.setAmount(totalNotTaxAmount);
        a.setTaxAmount(synthesizeSumAmount.subtract(totalNotTaxAmount));

        dto.setData(a);
        // 发送请求
        String content = JSON.toJSONString(dto);
        farArg.append(content);
        log.error("推送大宗临购计划：" + content);
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.PullDzPlan;
        LogUtil.writeInfoLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, null, SynthesizeTemporaryServiceImpl.class);
        com.scrbg.common.utils.R<Map> r = null;
        String planNo  = null;
        try {
            r = restTemplateUtils.postPCWP2(url, dto);
        } catch (Exception e) {
                    LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, null, e.getMessage(), SynthesizeTemporaryServiceImpl.class);
                    //throw new BusinessException("【远程异常】：" + e.getMessage());
            if (e.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyDZPlan(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, null, e.getMessage(), SynthesizeTemporaryServiceImpl.class);
                    log.error("推送计划报错：" + e.getMessage());
                    throw new BusinessException("【远程异常】：" + e.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, null, e.getMessage(), SynthesizeTemporaryServiceImpl.class);
                log.error("推送临购报错：" + e.getMessage());
                throw new BusinessException("【远程异常】：" + e.getMessage());
            }
        }
        if (r.getCode() == null || r.getCode() != 200) {
            if (r.getMessage().contains("Read timed out")){
              planNo  = pcwp1Servise.pcwp1VerifyDZPlan(mallConfig.prodPcwp2Url02, idStr);
                if (planNo== null){
                    LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, r, r.getMessage(), SynthesizeTemporaryServiceImpl.class);
                    log.error("推送大宗临购计划：" + r.getMessage());
                    throw new BusinessException("【远程异常】：" + r.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryPlan", id, dto, r, r.getMessage(), SynthesizeTemporaryServiceImpl.class);
                log.error("推送大宗临购计划：" + r.getMessage());
                throw new BusinessException("【远程异常】：" + r.getMessage());
            }
        }
        log.warn("推送大宗临购返回：" + r);
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(SynthesizeTemporaryServiceImpl.class.getName());
        iLog.setMethodName("submitSynthesizeTemporaryPlan"+planNo);
        iLog.setLocalArguments(JSON.toJSONString(id));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setErrorInfo(null);
        interfaceLogsService.create(iLog);
        byId.setState(6);
        update(byId);
    }



    /**
     * 导出excel
     *
     * @param id
     * @param response
     */
    @Override
    public void exportExcel(String id, HttpServletResponse response) {
        SynthesizeTemporary byId = getById(id);
        List<SynthesizeTemporaryDtl> list = synthesizeTemporaryDtlService.lambdaQuery().eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, id).list();
        ArrayList<SynthesizeTemporaryExportExcelItemVO> dtls = new ArrayList<>();
        for (SynthesizeTemporaryDtl synthesizeTemporaryDtl : list) {
            SynthesizeTemporaryExportExcelItemVO dtl = new SynthesizeTemporaryExportExcelItemVO();
            BeanUtils.copyProperties(synthesizeTemporaryDtl,dtl);
            dtls.add(dtl);
        }
        String src = mallConfig.templateFormUrl;
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("orgName",byId.getOrgName());
        dataMap.put("supplierOrgName",byId.getSupplierOrgName());
        dataMap.put("paymentWeek",byId.getPaymentWeek());
        dataMap.put("outPhaseInterest",byId.getOutPhaseInterest());
        dataMap.put("receiverAddress",byId.getReceiverAddress());
        dataMap.put("totalAmount",byId.getSynthesizeSumAmount());
        dataMap.put("dataList",dtls);
        try {
            if(byId.getBillType() == 1) {
                ExcelForWebUtil.exportExcel(response, dataMap, "大宗临购清单（浮动价格）模板.xlsx", src, "大宗临购清单（浮动价格）.xlsx");
            }
            if(byId.getBillType() == 2){
                ExcelForWebUtil.exportExcel(response, dataMap, "大宗临购清单（固定价格）模板.xlsx", src, "大宗临购清单（固定价格）.xlsx");
            }
        } catch (Exception e) {
            throw new BusinessException("导出失败！"+ e.getMessage());
        }
    }

    /**
     * 根据pcwp计划id获取组装计划vo
     *
     * @param id
     * @return
     */
    @Override
    public GetSynthesizeTemporaryPlanDetailVO getSynthesizeTemporaryPlanDetail(String id) {
        R<Map> r = null;
        try {
            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.PullDzPlanReturn + id);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】：" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            log.error("【远程异常】：" + r.getMessage());
            throw new BusinessException("【远程异常】：" + r.getMessage());
        }
        String s = JSON.toJSONString(r.getData());
        BulkRetailPlanEX ex = JSON.parseObject(s, BulkRetailPlanEX.class);
        // 处理数据返回对象
        String sourceBillId = ex.getSourceBillId();
        if(StringUtils.isBlank(sourceBillId)) {
            throw new BusinessException("大宗清单id不存在！");
        }
        SynthesizeTemporary byId = getById(sourceBillId);
        if(byId == null ) {
            throw new BusinessException("大宗清单信息不存在！");
        }
        GetSynthesizeTemporaryPlanDetailVO vo = new GetSynthesizeTemporaryPlanDetailVO();
        BeanUtils.copyProperties(byId,vo);
        // 给pcwp的vo部分赋值
        vo.setBillId(ex.getBillId());
        vo.setBillNo(ex.getBillNo());
        vo.setSourceBillId(ex.getSourceBillId());
        vo.setSourceBillNo(ex.getSourceBillNo());
        vo.setRemarks(ex.getRemarks());
        vo.setTotalAmount(ex.getTotalAmount());
        vo.setPlanDate(ex.getPlanDate());
        vo.setAmount(ex.getAmount());
        vo.setTaxRate(ex.getTaxRate());
        vo.setOrgShort(ex.getOrgShort());
        vo.setCreditCode(ex.getCreditCode());
        vo.setSupplierName(ex.getSupplierName());
        List<GetSynthesizeTemporaryPlanDetailItemVO> vos = new ArrayList<>();
        List<BulkRetailPlanDtlEX> details = ex.getDetails();
        for (BulkRetailPlanDtlEX detail : details) {
            String sourceDtlId = detail.getSourceDtlId();
            if(StringUtils.isBlank(sourceDtlId)) {
                throw new BusinessException("大宗清单明细id不存在！");
            }
            SynthesizeTemporaryDtl dtl = synthesizeTemporaryDtlService.getById(sourceDtlId);
            if(dtl == null) {
                throw new BusinessException("大宗清单明细信息不存在！");
            }
            GetSynthesizeTemporaryPlanDetailItemVO dtlVO= new GetSynthesizeTemporaryPlanDetailItemVO();
            BeanUtils.copyProperties(dtl,dtlVO);
            dtlVO.setDtlId(detail.getDtlId());
            dtlVO.setBillId(detail.getBillId());
            dtlVO.setAmount(detail.getAmount());
            // 材质使用pcwp修改的材质
            dtlVO.setTexture(detail.getTexture());
            dtlVO.setSourceDtlId(sourceDtlId);
            dtlVO.setConsumeAmount(detail.getConsumeAmount());
            dtlVO.setNotConsumeAmount(detail.getNotConsumeAmount());
            dtlVO.setReceivedQuantity(detail.getReceivedQuantity());
            if(mallConfig.isCountPlanOrderNum == 1) {
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtlVO.getDtlId());
                dtlVO.setReceivedQuantity(qty);

//                Map map1 =  ordersService.getOrderUseCountMapBySelectPlanDtlId(dtlVO.getDtlId());
//                BigDecimal orderQty = new BigDecimal(map1.get("orderQty").toString());
//                BigDecimal confirmCounts = new BigDecimal(map1.get("confirmCounts").toString());
//                dtlVO.setReceivedQuantity(orderQty);
//                dtlVO.setConfirmCounts(confirmCounts);



            }


            vos.add(dtlVO);
        }
        vo.setDetails(vos);
        return vo;



    }

    /**
     * pcwp删除草稿大宗临购计划时商城修改状态为可推送
     * @param id
     */
    @Override
    public void updateStayPush(String id) {
        SynthesizeTemporary byId = synthesizeTemporaryService.getById(id);
        if(byId != null) {
            if(byId.getState() == 6) {
                byId.setState(3);
                synthesizeTemporaryService.update(byId);
                // 修改明细竞价数据
                synthesizeTemporaryDtlService.lambdaUpdate()
                        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId,byId.getSynthesizeTemporaryId())
                        .set(SynthesizeTemporaryDtl::getIsBidding,0)
                        .update();
            }
        }

    }

    /**
     * 审核单据
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditBusiness(AuditBusinessDTO dto) {
        String id = dto.getId();
        SynthesizeTemporary byId = getById(id);
        if(byId == null) {
            throw new BusinessException("单据不存在！");
        }
        Integer isOpen = dto.getIsOpen();
        // 通过
        if (isOpen == 1) {
            AuditRecord auditRecord = new AuditRecord();
            auditRecord.setRelevanceType(7);
            auditRecord.setRelevanceId(id);
            auditRecord.setResultType(1);
            auditRecord.setAuditType(1);
            auditRecord.setAuditResult("【同意】");
            auditRecordService.create(auditRecord);
            // 审核通过
            byId.setState(3);
            update(byId);
        }
        // 不通过
        if (isOpen == 0) {
            AuditRecord auditRecord = new AuditRecord();
            String auditResult = dto.getAuditResult();
            auditRecord.setRelevanceType(7);
            auditRecord.setRelevanceId(id);
            auditRecord.setResultType(2);
            auditRecord.setAuditType(1);
            auditRecord.setAuditResult("【拒绝】" + auditResult);
            auditRecordService.create(auditRecord);
            // 审核不通过
            byId.setState(4);
            update(byId);
            // 修改明细竞价数据
            //synthesizeTemporaryDtlService.lambdaUpdate()
            //        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId,byId.getSynthesizeTemporaryId())
            //        .set(SynthesizeTemporaryDtl::getIsBidding,0)
            //        .update();
        }

    }

    /**
     * 供应商拒绝单据
     * @param dto
     */
    @Override
    public void refuseBusiness(AuditBusinessDTO dto) {
        String id = dto.getId();
        SynthesizeTemporary byId = getById(id);
        if(byId == null) {
            throw new BusinessException("单据不存在！");
        }
        Integer isOpen = dto.getIsOpen();
        if(isOpen != null && isOpen == 0 && byId.getState() == 3) {
            byId.setState(5);
            byId.setRefuseRes("【拒绝】" + dto.getAuditResult());
            update(byId);
            // 修改明细竞价数据
            synthesizeTemporaryDtlService.lambdaUpdate()
                    .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId,byId.getSynthesizeTemporaryId())
                    .set(SynthesizeTemporaryDtl::getIsBidding,0)
                    .update();
        }

    }

    /**
     * 物资分公司查看所有的大宗清单
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q) {
        String keywords = (String) jsonObject.get("keywords");
        String synthesizeTemporarySn = (String) jsonObject.get("synthesizeTemporarySn");
        String orgName = (String) jsonObject.get("orgName");
        Integer state = (Integer) jsonObject.get("state");
        Integer billType = (Integer) jsonObject.get("billType");
        ArrayList<Integer> states = (ArrayList<Integer>) jsonObject.get("states");
        String startCreateDate = (String) jsonObject.get("startCreateTime");
        String endCreateDate = (String) jsonObject.get("endCreateTime");
        String startSubmitTime = (String) jsonObject.get("startSubmitTime");
        String endSubmitTime = (String) jsonObject.get("endSubmitTime");
        String startAuditTime = (String) jsonObject.get("startAuditTime");
        String endAuditTime = (String) jsonObject.get("endAuditTime");
        if (org.apache.commons.lang.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(SynthesizeTemporary::getSynthesizeTemporarySn, keywords)
                        .or()
                        .like(SynthesizeTemporary::getOrgName, keywords)
                        .or()
                        .like(SynthesizeTemporary::getSupplierOrgName, keywords);
            });
        }
        q.eq(state != null,SynthesizeTemporary::getState,state);
        q.eq(billType != null,SynthesizeTemporary::getBillType,billType);
        q.in(CollectionUtils.isNotEmpty(states),SynthesizeTemporary::getState,states);
        q.like(StringUtils.isNotBlank(synthesizeTemporarySn),SynthesizeTemporary::getSynthesizeTemporarySn,synthesizeTemporarySn);
        q.like(StringUtils.isNotBlank(orgName),SynthesizeTemporary::getOrgName,orgName);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startCreateDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endCreateDate), SynthesizeTemporary::getGmtCreate, startCreateDate, endCreateDate);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startAuditTime) && org.apache.commons.lang.StringUtils.isNotEmpty(endAuditTime), SynthesizeTemporary::getAuditTime, startAuditTime, endAuditTime);
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startSubmitTime) && org.apache.commons.lang.StringUtils.isNotEmpty(endSubmitTime), SynthesizeTemporary::getSubmitTime, startSubmitTime, endSubmitTime);
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        if (orderBy == null || orderBy == 1) {
            q.orderByDesc(SynthesizeTemporary::getGmtCreate);
        } else if (orderBy == 2) {
            q.orderByDesc(SynthesizeTemporary::getAuditTime);
        } else if (orderBy == 3) {
            q.orderByDesc(SynthesizeTemporary::getSubmitTime);
        }
        IPage<SynthesizeTemporary> page = this.page(
                new Query<SynthesizeTemporary>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 供应商拒绝单据给收货单位
     * @param dto
     */
    @Override
    public void auditRefuseOrg(AuditBusinessDTO dto) {
        String id = dto.getId();
        SynthesizeTemporary byId = getById(id);
        if(byId == null) {
            throw new BusinessException("单据不存在！");
        }
        if(byId.getState() == 1 || byId.getState() == 4) {
            Integer isOpen = dto.getIsOpen();
            // 拒绝
            if (isOpen == 0) {
                AuditRecord auditRecord = new AuditRecord();
                String auditResult = dto.getAuditResult();
                auditRecord.setRelevanceType(9);
                auditRecord.setRelevanceId(id);
                auditRecord.setResultType(2);
                auditRecord.setAuditType(1);
                auditRecord.setAuditResult("【拒绝】" + auditResult);
                auditRecordService.create(auditRecord);
                byId.setState(11);
                update(byId);
                // 修改明细竞价数据
                //synthesizeTemporaryDtlService.lambdaUpdate()
                //        .eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId,byId.getSynthesizeTemporaryId())
                //        .set(SynthesizeTemporaryDtl::getIsBidding,0)
                //        .update();
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBidding(SynthesizeTemporaryDto dto) {
        String biddingExplain = dto.getBiddingExplain();
        Integer type = dto.getType();
        if (biddingExplain != null) {
            if (biddingExplain.length() > 2500) {
                throw new BusinessException("竞价说明内容过大！");
            }
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        // 检验是自营店
        String shopId = user.getShopId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getIsBusiness).one();
        if (shop == null) {
            throw new BusinessException("未找到店铺！");
        }
        if (shop.getIsBusiness() != 1) {
            throw new BusinessException("店铺不是自营店不能生成竞价！");
        }
        BiddingPurchase bid = new BiddingPurchase();
        BeanUtils.copyProperties(dto, bid);
        bid.setBiddingSourceType(3);
        // 包含年份的编号
        String sn= CodeGenerator.generateBidCode(13);
        //Integer count = biddingPurchaseService.lambdaQuery().like(BiddingPurchase::getBiddingSn, sn).count();
        // TODO 找到最大编号
        List<BiddingPurchase> bids = biddingPurchaseService.lambdaQuery().like(BiddingPurchase::getBiddingSn, sn).list();
        int count = 0;
        //List<String> bidSnList = bids.stream().map(BiddingPurchase::getBiddingSn).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bids)){
            Collections.sort(bids);
            BiddingPurchase biddingPurchase = bids.get(0);
            count = biddingPurchase.currentCount();
        }
        String biddingPurchaseNo=CodeGenerator.generateBidCodeNum(sn,count);
        bid.setBiddingSn(biddingPurchaseNo);
        bid.setProductType(2);
        bid.setType(type);
        bid.setState(0);
        bid.setShopId(user.getShopId());
        bid.setBiddingState(1);
        bid.setPublicityState(0);
        bid.setCreateOrgId(user.getEnterpriseId());
        bid.setCreateOrgName(user.getEnterpriseName());
        // 处理竞价商品
        List<SynthesizeTemporaryDtl> dtls = dto.getSynthesizeTemporaryDtlList();
        if (CollectionUtils.isEmpty(dtls)){
            throw new BusinessException("竞价商品不能为空！");
        }
        String synthesizeTemporaryId = dtls.get(0).getSynthesizeTemporaryId();
        SynthesizeTemporary temporary = getById(synthesizeTemporaryId);
        bid.setSynthesizeTemporarySn(temporary.getSynthesizeTemporarySn());
        biddingPurchaseService.save(bid);
        temporary.setBidStatus(1);
        boolean update = updateById(temporary);
        if (!update){
            throw new BusinessException("更新错误");
        }
        List<BiddingProduct> products = new ArrayList<>();
        for (SynthesizeTemporaryDtl dtl : dtls) {
            BiddingProduct bidP = new BiddingProduct();
            BeanUtils.copyProperties(dtl, bidP);
            // 材质
            bidP.setProductTexture(dtl.getTexture());
            bidP.setReferencePrice(dtl.getMaxPrice());
            bidP.setClassPathName(dtl.getClassNamePath());
            bidP.setNum(dtl.getQty());
            bidP.setSynthesizeTemporarySn(temporary.getSynthesizeTemporarySn());
            bidP.setBiddingId(bid.getBiddingId());
            bidP.setBiddingSn(bid.getBiddingSn());
            bidP.setState(null);
            bidP.setCreateOrgId(user.getEnterpriseId());
            bidP.setCreateOrgName(user.getEnterpriseName());
            bidP.setNetPrice(dtl.getBidNetPrice());
            bidP.setGmtCreate(new Date());
            products.add(bidP);
            dtl.setIsBidding(1);
        }
        biddingProductService.saveBatch(products);
        synthesizeTemporaryDtlService.updateBatchById(dtls);
        if (type == 2) {
            //  邀请竞价
            List<BiddingInvitationRelevance> suppliers = dto.getSuppliers();
            if (CollectionUtils.isEmpty(suppliers)){
                throw new BusinessException("邀请竞价请至少选择一个供应商");
            }
            for (BiddingInvitationRelevance supplier : suppliers) {
                supplier.setBiddingSn(biddingPurchaseNo);
            }
            boolean b = biddingInvitationRelevanceService.saveBatch(suppliers);
            if (!b) {
                throw new BusinessException("邀请供应商异常");
            }

        }

    }

    @Override
    public boolean updateState(SynthesizeTemporary updateState) {
        SynthesizeTemporary supplierBilBySn = getSupplierBilBySn(updateState.getSynthesizeTemporarySn());
        if (supplierBilBySn == null) {
            throw new BusinessException("未找到该清单！");
        }
        if (supplierBilBySn.getIsDelete() == 1) {
            throw new BusinessException("该清单已删除！");
        }
        supplierBilBySn.setState(updateState.getState());
        return updateById(supplierBilBySn);
    }
}
