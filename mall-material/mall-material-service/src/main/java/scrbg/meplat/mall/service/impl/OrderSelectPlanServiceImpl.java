package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import io.seata.common.util.StringUtils;
import org.docx4j.wml.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderSelectPlanMapper;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-02-28
 */
@Service
public class OrderSelectPlanServiceImpl extends ServiceImpl<OrderSelectPlanMapper, OrderSelectPlan> implements OrderSelectPlanService {

    @Autowired
    OrderItemService orderItemService;
    @Autowired
    OrdersService ordersService;

    @Autowired
    PlanService planService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> queryWrapper) {
        IPage<OrderSelectPlan> page = this.page(new Query<OrderSelectPlan>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void create(OrderSelectPlan orderSelectPlan) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderSelectPlan);
    }

    @Override
    public void update(OrderSelectPlan orderSelectPlan) {
        super.updateById(orderSelectPlan);
    }


    @Override
    public OrderSelectPlan getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 恢复计划数量
     *
     * @param orderItemId
     */
    @Override
    public void recoverPlanNum(List<String> orderItemId) {
        List<OrderSelectPlan> orderSelectPlans = lambdaQuery().in(OrderSelectPlan::getOrderItemId, orderItemId).list();
        if (CollectionUtils.isEmpty(orderSelectPlans)) {
            throw new BusinessException(400, "订单计划不存在！");
        } else {
            for (OrderSelectPlan orderSelectPlan : orderSelectPlans) {

            }
        }
    }

    @Override
    public OrderSelectPlan getDataByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderSelectPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderSelectPlan::getOrderSn, orderSn);
        List<OrderSelectPlan> list = list(wrapper);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 获取可对账的合同或计划列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        q.eq(OrderSelectPlan::getOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        String keywords = (String) jsonObject.get("keywords");
        String storageName = (String) jsonObject.get("storageName");
        Integer productType = (Integer) jsonObject.get("productType");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getStorageName, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getBillNo, keywords.trim());
            });
        }

        if (StringUtils.isNotBlank(storageName)) {
            q.eq(OrderSelectPlan::getStorageName, storageName);
        }

        if (productType == null) {
            throw new BusinessException("请携带类型！");
        }

        q.eq(OrderSelectPlan::getProductType, productType);
        q.select(OrderSelectPlan::getBillId, OrderSelectPlan::getBillNo, OrderSelectPlan::getStorageName,
                OrderSelectPlan::getOrderId, OrderSelectPlan::getGmtCreate, OrderSelectPlan::getContractNo,
                OrderSelectPlan::getContractId, OrderSelectPlan::getProductType, OrderSelectPlan::getOrgId,
                OrderSelectPlan::getOrgName, OrderSelectPlan::getOrderSelectPlanId);
        q.groupBy(OrderSelectPlan::getBillId, OrderSelectPlan::getBillNo, OrderSelectPlan::getStorageName);
        q.orderByDesc(OrderSelectPlan::getGmtCreate);
        IPage<OrderSelectPlan> page = this.page(new Query<OrderSelectPlan>().getPage(jsonObject), q);
        // 优化：批量查询订单信息，避免N+1查询问题，并过滤只有已审核计划的记录
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<OrderSelectPlan> validRecords = new ArrayList<>();
            for (OrderSelectPlan record : page.getRecords()) {
                // 检查计划是否已审核
                if (StringUtils.isNotBlank(record.getBillNo())) {
                    Plan plan = planService.lambdaQuery()
                            .eq(Plan::getBillNo, record.getBillNo())
                            .eq(Plan::getState, "2") // 只查询已审核的计划
                            .one();
                    if (plan != null) {
                        Orders byId = ordersService.getById(record.getOrderId());
                        record.setTaxRate(byId.getTaxRate());
                        validRecords.add(record);
                    }
                }
            }
            page.setRecords(validRecords);
            page.setTotal(validRecords.size());
//                String shortCode = record.getShortCode();
//                String creditCode = record.getCreditCode();
//                if(StringUtils.isNotBlank(shortCode)) {
//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getShortCode, shortCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
//                    record.setTaxRate(one.getTaxRate());
//                }
//                if(StringUtils.isNotBlank(creditCode)) {
//                    EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, creditCode)
//                            .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate).one();
//                    record.setTaxRate(one.getTaxRate());
//                }
            }
        }

        return new PageUtils(page);
    }

    /**
     * 获取可对账的合同或计划列表（获取供应商的）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils supplierGetContactPlanPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        // 查询机构信息
        EnterpriseInfo e = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getInteriorId, EnterpriseInfo::getShortCode, EnterpriseInfo::getSocialCreditCode).one();
        Integer isInterior = user.getIsInterior();
//        if (isInterior == 1) {
//            // 内部供应商
//            q.eq(OrderSelectPlan::getShortCode, e.getShortCode());
//        }
//        if (isInterior == 0) {
//            // 外部供应商
//            q.eq(OrderSelectPlan::getCreditCode, e.getSocialCreditCode());
//        }

        String keywords = (String) jsonObject.get("keywords");
        String orgName = (String) jsonObject.get("orgName");
        if (StringUtils.isNotBlank(orgName)) {
            q.eq(OrderSelectPlan::getOrgName, orgName);
        }
        Integer productType = (Integer) jsonObject.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(OrderSelectPlan::getStorageName, keywords.trim())
                        .or()
                        .like(OrderSelectPlan::getBillNo, keywords.trim());
            });
        }
        if (productType == null) {
            throw new BusinessException("请携带类型！");
        } else {
            switch (productType) {
                case 0: // 零星采购 (1, 10)
                    q.in(OrderSelectPlan::getProductType, 0, 10);
                    break;
                case 1: // 大宗临购 (2, 12)
                    q.in(OrderSelectPlan::getProductType, 1, 13);
                    break;
                case 2: // 周转材料 (3)
                    q.eq(OrderSelectPlan::getProductType, 2);
                    break;
                default:
                    // 如果 productType 不在预期范围内，可以选择不添加条件或抛出异常
                    log.warn("未知的 productType 值: {}" + productType);
                    break;
            }
        }
//        q.eq(OrderSelectPlan::getProductType, productType);

        // 修复GROUP BY语法错误：根据不同产品类型指定对应的SELECT字段
        q.select(OrderSelectPlan::getBillId, OrderSelectPlan::getBillNo, OrderSelectPlan::getOrderSn,
                OrderSelectPlan::getOrderId, OrderSelectPlan::getGmtCreate, OrderSelectPlan::getProductType,
                OrderSelectPlan::getOrgId, OrderSelectPlan::getOrgName, OrderSelectPlan::getOrderSelectPlanId);
//        q.groupBy(OrderSelectPlan::getBillId, OrderSelectPlan::getBillNo, OrderSelectPlan::getOrderSn, OrderSelectPlan::getOrderId);
        q.orderByDesc(OrderSelectPlan::getGmtCreate);
        IPage<OrderSelectPlan> page = this.page(new Query<OrderSelectPlan>().getPage(jsonObject), q);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<OrderSelectPlan> validRecords = new ArrayList<>();
            for (OrderSelectPlan record : page.getRecords()) {
                Plan plan = planService.getByBillId(record.getBillId());
                // 只保留已审核的计划
                if (plan != null && "2".equals(plan.getState())) {
                    record.setPlan(plan);//通过plan得知是否推送到PCWP
                    Orders byId = ordersService.getById(record.getOrderId());
                    record.setTaxRate(byId.getTaxRate());
                    validRecords.add(record);
                }
            }
            page.setRecords(validRecords);
            page.setTotal(validRecords.size());
        }
        return new PageUtils(page);
    }

    @Override
    public OrderSelectPlan getDataByorderItemId(String orderItemId) {
        LambdaQueryWrapper<OrderSelectPlan> q = new LambdaQueryWrapper<>();
        q.eq(OrderSelectPlan::getOrderItemId, orderItemId);
        OrderSelectPlan one = getOne(q);
        return one;
    }

    @Override
    public PageUtils supplierGetEnterprisePageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();

        // 查询机构信息
        EnterpriseInfo e = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getInteriorId, EnterpriseInfo::getShortCode, EnterpriseInfo::getSocialCreditCode)
                .one();

        Integer isInterior = user.getIsInterior();
        String keywords = (String) jsonObject.get("keywords");
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType == null) {
            throw new BusinessException("请携带类型！");
        }

        // 使用内存去重方案，确保完全去重
        // 第一步：查询所有符合条件的记录
        LambdaQueryWrapper<OrderSelectPlan> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(OrderSelectPlan::getProductType, productType)
//                .eq(OrderSelectPlan::getIsDelete, 0);
        queryWrapper.eq(OrderSelectPlan::getIsDelete, 0);
        if (productType != null) {
            switch (productType) {
                case 0: // 零星采购 (1, 10)
                    queryWrapper.in(OrderSelectPlan::getProductType, 0, 10);
                    break;
                case 1: // 大宗临购 (2, 12)
                    queryWrapper.in(OrderSelectPlan::getProductType, 1, 13);
                    break;
                case 2: // 周转材料 (3)
                    queryWrapper.eq(OrderSelectPlan::getProductType, 2);
                    break;
                default:
                    break;
            }
        }
        // 内部供应商条件
        if (isInterior == 1) {
            queryWrapper.eq(OrderSelectPlan::getShortCode, e.getShortCode());
        }
        // 根据产品类型添加条件
        queryWrapper.isNotNull(OrderSelectPlan::getBillId);
        // 关键词搜索
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.like(OrderSelectPlan::getOrgName, keywords.trim());
        }
        // 按时间降序排序
        queryWrapper.orderByDesc(OrderSelectPlan::getGmtCreate);
        // 查询所有记录
        List<OrderSelectPlan> allRecords = this.list(queryWrapper);
        // 第二步：过滤只有已审核计划的记录，然后使用LinkedHashMap按org_name去重，保留最新的记录
        Map<String, OrderSelectPlan> uniqueRecords = new LinkedHashMap<>();
        for (OrderSelectPlan record : allRecords) {
            // 检查计划是否已审核
            if (StringUtils.isNotBlank(record.getBillNo())) {
                Plan plan = planService.lambdaQuery()
                        .eq(Plan::getBillNo, record.getBillNo())
                        .eq(Plan::getState, "2") // 只查询已审核的计划
                        .one();
                if (plan != null) {
                    String orgName = record.getOrgName();
                    // 只保留每个org_name的第一条记录（由于已按时间降序排序，第一条就是最新的）
                    if (!uniqueRecords.containsKey(orgName)) {
                        uniqueRecords.put(orgName, record);
                    }
                }
            }
        }
        // 第三步：转换为List
        List<OrderSelectPlan> deduplicatedList = new ArrayList<>(uniqueRecords.values());
        // 第四步：使用MyBatis-Plus的IPage进行手动分页
        IPage<OrderSelectPlan> pageParam = new Query<OrderSelectPlan>().getPage(jsonObject);
        long current = pageParam.getCurrent();
        long size = pageParam.getSize();
        long total = deduplicatedList.size();
        // 计算分页范围
        int start = (int) ((current - 1) * size);
        int end = (int) Math.min(start + size, total);
        // 获取当前页数据
        List<OrderSelectPlan> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = deduplicatedList.subList(start, end);
        }
        // 构建MyBatis-Plus的IPage结果
        IPage<OrderSelectPlan> resultPage = new Page<>(current, size, total);
        resultPage.setRecords(pageRecords);
        return new PageUtils(resultPage);
    }

    @Override
    public PageUtils getContactSupplierPageList(JSONObject jsonObject, LambdaQueryWrapper<OrderSelectPlan> q) {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String keywords = (String) jsonObject.get("keywords");
        Integer productType = (Integer) jsonObject.get("productType");

        if (productType == null) {
            throw new BusinessException("请携带类型！");
        }

        // 使用自定义SQL查询，只返回已审核计划的供应商
        Page<OrderSelectPlan> page = new Query<OrderSelectPlan>().getPage(jsonObject);
        List<OrderSelectPlan> records = baseMapper.getContactSupplierWithAuditedPlan(
                page,
                currentUser.getOrgId(),
                productType,
                StringUtils.isNotBlank(keywords) ? keywords.trim() : null
        );

        page.setRecords(records);
        page.setTotal(records.size());
        return new PageUtils<>(page);
    }


    @Override
    public List<Orders> selectOrderListByPlanNo(JSONObject jsonObject) {
        LambdaQueryChainWrapper<OrderSelectPlan> q = lambdaQuery();
        q.eq(OrderSelectPlan::getOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        String planNo = (String) jsonObject.get("planNo");
        Integer productType = (Integer) jsonObject.get("productType");
        if (productType == null) {
            throw new BusinessException("请携带类型！");
        }
        q.eq(OrderSelectPlan::getProductType, productType);
        q.isNotNull(OrderSelectPlan::getBillId);
        if (StringUtils.isNotBlank(planNo)) {
            q.eq(OrderSelectPlan::getBillNo, planNo);
        }
        q.select(OrderSelectPlan::getOrderId, OrderSelectPlan::getBillNo).groupBy(OrderSelectPlan::getOrderId);
        List<OrderSelectPlan> list = q.list();
        if (list.size() > 0) {
            // 过滤只有已审核计划的订单
            List<String> validOrderIds = new ArrayList<>();
            for (OrderSelectPlan osp : list) {
                if (StringUtils.isNotBlank(osp.getBillNo())) {
                    Plan plan = planService.lambdaQuery()
                            .eq(Plan::getBillNo, osp.getBillNo())
                            .eq(Plan::getState, "2") // 只查询已审核的计划
                            .one();
                    if (plan != null) {
                        validOrderIds.add(osp.getOrderId());
                    }
                }
            }

            if (!validOrderIds.isEmpty()) {
                List<Orders> ordersList = ordersService.lambdaQuery().in(Orders::getOrderId, validOrderIds).
                        select(Orders::getOrderSn, Orders::getState,
                                MustBaseEntity::getGmtCreate, Orders::getSuccessDate, Orders::getSupplierName, Orders::getUntitled)
                        .orderByAsc(Orders::getState).list();
                return ordersList;
            }
        }
        return new ArrayList<>();
    }
}
