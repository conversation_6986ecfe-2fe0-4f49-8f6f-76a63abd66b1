<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderShipDtlMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderShipDtl" id="OrderShipDtlMap">
        <result property="dtlId" column="dtl_id"/>
        <result property="orderId" column="order_Id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="productName" column="product_name"/>
        <result property="productSn" column="product_sn"/>
        <result property="productPrice" column="product_price"/>
        <result property="shipments" column="shipments"/>
        <result property="sendTime" column="send_time"/>
        <result property="type" column="type"/>
        <result property="receiveTime" column="receive_time"/>
        <result property="mallType" column="mall_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="price" column="price"/>
        <result property="shipNum" column="ship_num"/>
        <result property="unshipNum" column="unship_num"/>
        <result property="totalPrice" column="total_price"/>
        <result property="totalSum" column="total_sum"/>
        <result property="billId" column="bill_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="productCategoryName" column="product_category_name"/>
    </resultMap>


    <select id="listByAffirmListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        INNER JOIN shop s on sh.shop_id = s.shop_id and s.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = sh.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0 AND  po.type = 'O' AND po.orglayertypenumber = 9
        <where>
            sh.type = 2
            <if test="dto.orgId != null and dto.orgId != ''">
                and po.`id` = #{dto.orgId}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and sh.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                and sh.`enterprise_name` LIKE CONCAT('%',#{dto.enterpriseName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.productType != null">
                and sh.`product_type` = #{dto.productType}
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or sh.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%'))
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
            <if test="dto.shopId != null and dto.shopId != ''">
                and sh.`shop_id` &gt;= #{shopId}
            </if>
        </where>
    </select>
    <select id="listByAffirmList" resultType="scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO">
        select * from (
        SELECT s.shop_name as shopName,
        osd.dtl_id as dtlId,
        sh.enterprise_name,
        sh.supplier_name as supplierName,
        sh.product_type as productType,
        sh.enterprise_name as enterpriseName,
        osd.product_name as productName,
        osd.order_sn as orderSn,
        osd.product_price as productPrice,
        osd.no_rate_price as noRatePrice,
        osd.ship_num as number,
        osd.return_counts as returnCounts,
        osd.total_amount as amount,
        osd.no_rate_amount as noRateAmount,
        osd.sku_name as skuName,
        osd.unit as unit,
        po.sortcode as sortCode,
        DATE_FORMAT(sh.confirm_time, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        AND sh.product_type != 12
        INNER JOIN shop s on sh.shop_id = s.shop_id and s.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = sh.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0 AND  po.type = 'O' AND po.orglayertypenumber = 9
        <where>
            sh.type = 2
            <if test="dto.orgId != null and dto.orgId != ''">
                and po.`id` = #{dto.orgId}
            </if>
            <if test="dto.sortCodes != null and !dto.sortCodes.isEmpty()">
                and po.`sortcode` in
                <foreach item="item" collection="dto.sortCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and sh.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                and sh.`enterprise_name` LIKE CONCAT('%',#{dto.enterpriseName},'%')
            </if>
            <if test="dto.productType != null ">
                and sh.`product_type` = #{dto.productType}
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        union All
        SELECT ore.shop_name as shopName,
        ori.order_return_item_id as dtlId,
        ore.enterprise_name,
        ore.supplier_name as supplierName,
        o.product_type as productType,
        ore.enterprise_name as enterpriseName,
        ori.product_name as productName,
        ore.order_sn as orderSn,
        ori.product_price as productPrice,
        ori.no_rate_price as noRatePrice,
        -ori.count as number,
        0,
        -ori.total_amount as amount,
        -ori.no_rate_amount as noRateAmount,
        ori.sku_name as skuName,
        ori.unit as unit,
        null as sortcode,
        DATE_FORMAT(ore.gmt_create, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_return_item ori
        INNER JOIN order_return ore
        ON ori.order_return_id = ore.order_return_id AND ori.is_delete = 0 AND ore.is_delete= 0
        and ore.source_type !=1
        INNER JOIN orders o ON ore.order_id = o.order_id and o.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = ore.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0
        <where>
            ore.state=3 and ore.is_out=1
            <if test="dto.orgId != null and dto.orgId != ''">
                and po.`id` = #{dto.orgId}
            </if>
            <if test="dto.sortCodes != null and !dto.sortCodes.isEmpty()">
                and po.`sortcode` in
                <foreach item="item" collection="dto.sortCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and ori.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.productType != null ">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and ore.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                and ore.`enterprise_name` LIKE CONCAT('%',#{dto.enterpriseName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and ore.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                AND (
                ori.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and ore.`gmt_create` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and ori.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and ori.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b order by b.finishDateStr desc
    </select>

    <select id="platformOutputExcel" resultType="scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO">
        select * from (
        SELECT s.shop_name as shopName,
        osd.dtl_id as dtlId,
        sh.enterprise_name,
        sh.supplier_name as supplierName,
        sh.product_type as productType,
        sh.enterprise_name as enterpriseName,
        osd.product_name as productName,
        osd.order_sn as orderSn,
        osd.product_price as productPrice,
        osd.no_rate_price as noRatePrice,
        osd.ship_num as number,
        osd.return_counts as returnCounts,
        osd.total_amount as amount,
        osd.no_rate_amount as noRateAmount,
        osd.sku_name as skuName,
        osd.unit as unit,
        po.sortcode as sortCode,
        DATE_FORMAT(sh.confirm_time, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        AND sh.product_type != 12
        INNER JOIN shop s on sh.shop_id = s.shop_id and s.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = sh.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0 AND  po.type = 'O' AND po.orglayertypenumber = 9
        <where>
            sh.type = 2
            <if test="dto.orgId != null and dto.orgId != ''">
                and po.`id` = #{dto.orgId}
            </if>
            <if test="dto.sortCodes != null and !dto.sortCodes.isEmpty()">
                and po.`sortcode` in
                <foreach item="item" collection="dto.sortCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and sh.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                and sh.`enterprise_name` LIKE CONCAT('%',#{dto.enterpriseName},'%')
            </if>
            <if test="dto.productType != null ">
                and sh.`product_type` = #{dto.productType}
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or s.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                or sh.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        union All
        SELECT ore.shop_name as shopName,
        ori.order_return_item_id as dtlId,
        ore.enterprise_name,
        ore.supplier_name as supplierName,
        o.product_type as productType,
        ore.enterprise_name as enterpriseName,
        ori.product_name as productName,
        ore.order_sn as orderSn,
        ori.product_price as productPrice,
        ori.no_rate_price as noRatePrice,
        -ori.count as number,
        0,
        -ori.total_amount as amount,
        -ori.no_rate_amount as noRateAmount,
        ori.sku_name as skuName,
        ori.unit as unit,
        null as sortcode,
        DATE_FORMAT(ore.gmt_create, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_return_item ori
        INNER JOIN order_return ore
        ON ori.order_return_id = ore.order_return_id AND ori.is_delete = 0 AND ore.is_delete= 0
        and ore.source_type !=1
        INNER JOIN orders o ON ore.order_id = o.order_id and o.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = ore.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0
        <where>
            ore.state=3 and ore.is_out=1
            <if test="dto.orgId != null and dto.orgId != ''">
                and po.`id` = #{dto.orgId}
            </if>
            <if test="dto.sortCodes != null and !dto.sortCodes.isEmpty()">
                and po.`sortcode` in
                <foreach item="item" collection="dto.sortCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and ori.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.productType != null ">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and ore.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.enterpriseName != null and dto.enterpriseName != ''">
                and ore.`enterprise_name` LIKE CONCAT('%',#{dto.enterpriseName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and ore.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                AND (
                ori.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and ore.`gmt_create` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and ori.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and ori.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b order by b.finishDateStr desc
    </select>


    <select id="shopListByAffirmListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        <where>
            sh.type = 2 and sh.suplier_id = #{dto.enterpriseId}
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')))
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>

    <select id="shopListByAffirmList" resultType="scrbg.meplat.mall.vo.platform.ShopListShipByAffirmListVO">
        SELECT
        osd.dtl_id as dtlId,
        osd.order_sn as orderSn
        osd.product_name as productName,
        osd.product_price as productPrice,
        osd.ship_num as number,
        osd.return_counts as returnCounts,
        osd.total_amount as amount,
        DATE_FORMAT(sh.confirm_time, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        <where>
            sh.type = 2 and sh.suplier_id = #{dto.enterpriseId}
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')))
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_amount` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_amount` &gt;= #{dto.abovePrice}
            </if>
        </where>
        order by sh.confirm_time desc
    </select>
    <select id="shopManageOutputExcel" resultType="scrbg.meplat.mall.vo.platform.ShopListShipByAffirmListVO">
        SELECT
        osd.dtl_id as dtlId,
        osd.product_name as productName,
        osd.product_price as productPrice,
        osd.ship_num as number,
        osd.return_counts as returnCounts,
        osd.total_price as amount,
        DATE_FORMAT(sh.confirm_time, '%Y-%m-%d %H:%i:%s') as finishDateStr
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        <where>
            sh.type = 2 and sh.supplier_id = #{dto.enterpriseId}
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%')))
            </if>
            <if test="dto.ids != null and !dto.ids.isEmpty()">
                and osd.`dtl_id` in
                <foreach item="item" collection="dto.ids" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        order by sh.confirm_time desc
    </select>
    <select id="getTwoOrderShipByBillid" resultType="scrbg.meplat.mall.entity.OrderShipDtl">
        SELECT
        osd.product_name,
        osd.sku_name,
        osd.unit,
        osd.ship_counts,
        osd.other_product_price AS productPrice,
        osd.other_total_amount AS totalAmount,
        CASE
        WHEN oi.product_type = 12 THEN oi.product_name
        ELSE oi.relevance_name
        END AS relevanceName
        FROM
        order_ship_dtl osd
        LEFT JOIN
        order_item oi ON oi.order_item_id = osd.order_item_id
        where osd.bill_id=#{billId}


    </select>
    <select id="getCountByOrderItemIdAndType" resultType="java.math.BigDecimal">
        select sum(os.ship_num) from order_ship_dtl osd
        LEFT JOIN order_ship os
        ON osd.order_id=os.order_id
        where osd.order_item_id=#{orderItemId} and os.type= #{type}
    </select>
    <select id="selectSumShipNum" resultType="java.math.BigDecimal">
        select sum(osd.ship_num) from order_ship_dtl osd left join order_ship os on osd.bill_id =os.bill_id
        where
        ${ew.sqlSegment}
    </select>
    <select id="selectSumShipCount" resultType="java.math.BigDecimal">
        select sum(osd.ship_counts) from order_ship_dtl osd left join order_ship os on osd.bill_id =os.bill_id
        where
        ${ew.sqlSegment}
    </select>
    <select id="findAllMaterialShipDtlByOrderShipId" resultType="scrbg.meplat.mall.vo.ship.MaterialShipDtlVo">
        select dtl.ship_num,p.sku_name,p.relevance_name AS materialName ,p.product_texture as texture,dtl.unit
        from order_ship_dtl dtl left join product p on dtl.product_id=p.product_id where dtl.bill_id=#{billId}
    </select>

    <select id="selCountAmount" resultType="java.math.BigDecimal">
        select sum(b.totalAmount) from (
        SELECT osd.total_amount as totalAmount
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        INNER JOIN shop s on sh.shop_id = s.shop_id and s.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = sh.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0 AND  po.type = 'O' AND po.orglayertypenumber = 9
        <where>
            sh.type = 2
            <if test="dto.sortCode != null and dto.sortCode != ''">
                and po.`sortcode`like CONCAT(#{dto.sortCode},'%')
            </if>
            <if test="dto.productType != null ">
                and sh.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and sh.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or sh.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%')
                OR sh.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR sh.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        union All
        SELECT -ori.total_amount as totalAmount
        FROM order_return_item ori
        INNER JOIN order_return ore ON ori.order_return_id = ore.order_return_id AND ori.is_delete = 0
        AND ore.is_delete= 0
        INNER JOIN orders o ON ore.order_id = o.order_id and o.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = ore.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0
        <where>ore.state=3 and ore.is_out=1
            <if test="dto.sortCode != null and dto.sortCode != ''">
                and po.`sortcode` like CONCAT(#{dto.sortCode},'%')
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and ori.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.productType != null ">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and ore.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and ore.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (ori.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or ore.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ore.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%')
                OR ore.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
            )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and ore.`gmt_create` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and ori.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and ori.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>
   <!--不含税总价-->
    <select id="selCountNoRateAmount" resultType="java.math.BigDecimal">
        select sum(b.noRateAmount) from (
        SELECT osd.no_rate_amount as noRateAmount
        FROM order_ship_dtl osd
        INNER JOIN order_ship sh ON osd.bill_id = sh.bill_id
        AND osd.is_delete = 0
        AND sh.is_delete = 0
        INNER JOIN shop s on sh.shop_id = s.shop_id and s.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = sh.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0 AND  po.type = 'O' AND po.orglayertypenumber = 9
        <where>
            sh.type = 2
            <if test="dto.sortCode != null and dto.sortCode != ''">
                and po.`sortcode` like CONCAT(#{dto.sortCode},'%')
            </if>
            <if test="dto.productType != null ">
                and sh.`product_type` = #{dto.productType}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and osd.`product_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and sh.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (osd.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or s.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or sh.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%')
                OR sh.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR sh.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and sh.`confirm_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        union All
        SELECT -ori.no_rate_amount as noRateAmount
        FROM order_return_item ori
        INNER JOIN order_return ore ON ori.order_return_id = ore.order_return_id AND ori.is_delete = 0
        AND ore.is_delete= 0
        INNER JOIN orders o ON ore.order_id = o.order_id and o.is_delete = 0
        INNER JOIN pcwp_orginfos po on po.name = ore.`enterprise_Name` AND po.mdmstate != -1 AND po.issealup = 0
        <where>ore.state=3 and ore.is_out=1
            <if test="dto.sortCode != null and dto.sortCode != ''">
                and po.`sortcode` like CONCAT(#{dto.sortCode},'%')
            </if>
            <if test="dto.productType != null ">
                and o.`product_type` = #{dto.productType}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and ore.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.shopName != null and dto.shopName != ''">
                and ore.`shop_name` LIKE CONCAT('%',#{dto.shopName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (ori.`product_name` LIKE CONCAT('%',#{dto.keywords},'%') or ore.`shop_name` LIKE
                CONCAT('%',#{dto.keywords},'%') or ore.`supplier_name` LIKE
                CONCAT('%',#{dto.keywords},'%')
                OR ore.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%')
                OR ore.`enterprise_name` LIKE CONCAT('%',#{dto.keywords},'%')
            )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and ore.`gmt_create` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and ori.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and ori.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>
    <select id="getMaterialShipDzDtlsByOrderShipId" resultType="scrbg.meplat.mall.vo.ship.MaterialShipDtlVo">
        select dtl.dtl_id, dtl.ship_num,
        oi.sku_name,oi.product_name AS materialName ,
        oi.texture as texture,
        dtl.unit
        from order_ship_dtl dtl
        left join order_item oi
        on dtl.order_item_id=oi.order_item_id
        where dtl.bill_id=#{billId}
    </select>

    <!-- 查询一级大宗临购可对账物资列表总数 -->
    <select id="getReconcilableMaterialListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship s ON d.bill_id = s.bill_id
        LEFT JOIN order_select_plan osp ON o.order_id = osp.order_id
        LEFT JOIN plan p ON p.bill_no = osp.bill_no
        LEFT JOIN (
            SELECT
                order_id,
                material_id,
                SUM(quantity) as reconciled_quantity
            FROM material_reconciliation_dtl
            WHERE is_delete = 0
            GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
          AND d.is_delete = 0
          AND s.is_delete = 0
          AND s.type = 2
          AND d.ship_num > 0
          AND d.receive_time IS NOT NULL
          AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        <if test="dto.keywords != null and dto.keywords != ''">
            AND d.product_name LIKE CONCAT('%', #{dto.keywords}, '%')
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null and dto.productType != ''">
            <choose>
                <when test="dto.productType == '1'">
                    AND o.product_type IN (1, 10)    -- 零星采购 (1, 10)
                </when>
                <when test="dto.productType == '2'">
                    AND o.product_type IN (2, 12)    -- 大宗临购 (2, 12)
                </when>
                <when test="dto.productType == '3'">
                    AND o.product_type = 3           -- 周转材料 (3)
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND d.order_sn IN
            <foreach item="item" collection="dto.orderSn.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询一级大宗临购可对账物资列表 -->
    <select id="getReconcilableMaterialList" resultType="scrbg.meplat.mall.vo.ship.ReconcilableMaterialVO">
        SELECT
            d.order_id as orderId,
            d.order_sn as orderSn,
            d.product_id as trade_id,
            d.product_name as materialName,
            oi.class_path_id as material_class_id,
            oi.class_path_name as material_class_name,
            oi.supplier_id,
            enter.enterprise_name as supplier_name,
            p.founder_id as purchaser_id,
            p.founder_name as purchaser_name,
            o.enterprise_id AS purchasing_org_id,
            o.enterprise_name AS purchasing_org_name,
            d.sku_name as spec,
            d.texture,
            d.unit,
            (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) as maxQuantity,
            d.order_item_id as orderItemId,
            d.receive_time as receiveDate,
            d.product_price as ratePrice,
            d.no_rate_price as noRatePrice,
            o.tax_rate as tax_rate,
            o.bill_type as type,
            o.remarks as remarks
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship s ON d.bill_id = s.bill_id
        LEFT JOIN order_item oi ON d.order_item_id = oi.order_item_id
        left Join enterprise_info enter on oi.supplier_id = enter.enterprise_id
        left join plan p on p.bill_id = o.plan_id
        LEFT JOIN (
            SELECT
                order_id,
                material_id,
                SUM(quantity) as reconciled_quantity
            FROM material_reconciliation_dtl
            WHERE is_delete = 0
            GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
          AND d.is_delete = 0
          AND s.is_delete = 0
          AND s.type = 2
          AND d.ship_num > 0
          AND d.receive_time IS NOT NULL
          AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        <if test="dto.keywords != null and dto.keywords != ''">
            AND d.product_name LIKE CONCAT('%', #{dto.keywords}, '%')
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null and dto.productType != ''">
            <choose>
                <when test="dto.productType == '1'">
                    AND o.product_type IN (1, 10)    -- 零星采购 (1, 10)
                </when>
                <when test="dto.productType == '2'">
                    AND o.product_type IN (2, 12)    -- 大宗临购 (2, 12)
                </when>
                <when test="dto.productType == '3'">
                    AND o.product_type = 3           -- 周转材料 (3)
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND d.order_sn IN
            <foreach item="item" collection="dto.orderSn.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY d.receive_time DESC
    </select>

</mapper>
