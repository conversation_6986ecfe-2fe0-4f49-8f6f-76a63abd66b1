<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.Role2Mapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.mall.entity.system.SysRole2">
        <id column="role_id" property="roleId" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="founder_id" property="founderId" jdbcType="VARCHAR"/>
        <result column="founder_name" property="founderName" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="mall_type" property="mallType" jdbcType="TINYINT"/>
        <result column="keyword" property="keyword" jdbcType="VARCHAR"/>
        <result column="org_search" property="orgSearch" jdbcType="TINYINT"/>
        <result column="category_type" property="categoryType" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_id" property="modifyId" jdbcType="VARCHAR"/>
        <result column="enterprise_id" property="enterpriseId" jdbcType="VARCHAR"/>
        <result column="jgtype" property="jgtype" jdbcType="VARCHAR"/>
        <result column="jgname" property="jgname" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getAllJglist" resultType="scrbg.meplat.mall.entity.system.SysJg">
        SELECT
        id, type, name, sort, is_delete from sys_jg
        WHERE is_delete = 0
        order by sort asc
    </select>

    <sql id="Base_Column_List">
        a.role_id, a.code, a.name, a.sort, a.state, a.remarks, a.gmt_create, a.gmt_modified,
        a.founder_id, a.founder_name, a.is_delete, a.mall_type, a.keyword, a.org_search,
        a.category_type, a.modify_name, a.modify_id, a.enterprise_id, a.jgtype, b.name jgname
    </sql>

    <sql id="Base_Column_List2">
        a.role_id, a.code, a.name, a.sort, a.state, a.remarks, a.gmt_create, a.gmt_modified,
        a.founder_id, a.founder_name, a.is_delete, a.mall_type, a.keyword, a.org_search,
        a.category_type, a.modify_name, a.modify_id, a.enterprise_id, a.jgtype
    </sql>

    <!-- 分页查询列表 -->
    <select id="selectRoleList" resultMap="BaseResultMap" parameterType="scrbg.meplat.mall.dto.system.SysRole2DTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_role1 a left join sys_jg b on a.jgtype = b.type
        WHERE a.is_delete = 0
        <if test="name != null and name != ''">
            AND a.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="categoryType != null and categoryType != ''">
            AND a.category_type = #{categoryType}
        </if>
        <if test="jgtype != null and jgtype != ''">
            AND a.jgtype = #{jgtype}
        </if>

        ORDER BY a.sort ASC
        <!-- 分页控制 -->
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}  <!-- MySQL语法 -->
            <!-- 如果是Oracle数据库，替换为：
            OFFSET #{offset} ROWS FETCH NEXT #{pageSize} ROWS ONLY -->
        </if>
    </select>

    <!-- 总数统计（保持不变） -->
    <select id="countRoleList" resultType="int" parameterType="scrbg.meplat.mall.dto.system.SysRole2DTO">
        SELECT COUNT(1)
        FROM sys_role1
        WHERE is_delete = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="categoryType != null and categoryType != ''">
            AND category_type = #{categoryType}
        </if>
        <if test="jgtype != null and jgtype != ''">
            AND jgtype = #{jgtype}
        </if>
    </select>

    <insert id="insertRole" parameterType="scrbg.meplat.mall.dto.system.SysRole2DTO">
        INSERT INTO sys_role1 (
        role_id, code, name, sort, state, remarks, gmt_create,
        founder_id, founder_name, is_delete, mall_type, keyword,
        org_search, category_type, enterprise_id, jgtype
        ) VALUES (
        #{roleId}, #{code}, #{name}, #{sort}, #{state}, #{remarks}, #{gmtCreate},
        #{founderId}, #{founderName}, #{isDelete}, #{mallType}, #{keyword},
        #{orgSearch}, #{categoryType}, #{enterpriseId}, #{jgtype}
        )
    </insert>

    <update id="updateRole" parameterType="scrbg.meplat.mall.dto.system.SysRole2DTO">
        UPDATE sys_role1
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="gmtModified != null">gmt_modified = #{gmtModified},</if>
            <if test="mallType != null">mall_type = #{mallType},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="orgSearch != null">org_search = #{orgSearch},</if>
            <if test="categoryType != null">category_type = #{categoryType},</if>
            <if test="modifyName != null">modify_name = #{modifyName},</if>
            <if test="modifyId != null">modify_id = #{modifyId},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
        </set>
        WHERE role_id = #{roleId}
    </update>
    <update id="deleteRole">
        UPDATE sys_role1
        SET is_delete = -1,
        gmt_modified = #{modifyTime},
        modify_id = #{modifyUserId},
        modify_name = #{modifyUserName}
        WHERE role_id = #{roleId}
    </update>

    <select id="selectRoleById" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List2"/>
        FROM sys_role1 a
        WHERE a.role_id = #{roleId} AND a.is_delete = 0
    </select>

    <select id="getRoleListAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List2"/>
        FROM sys_role1 a
        WHERE a.is_delete = 0
    </select>

</mapper>